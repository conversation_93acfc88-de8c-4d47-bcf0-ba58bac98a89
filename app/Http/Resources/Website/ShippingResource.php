<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\PriceResource;


class ShippingResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            'cityId' => $this->cityId,
            'shippingCarrierId' => $this->shippingCarrierId,
            'name' => $this->shippingCarrier->name,
            'slug' => $this->shippingCarrier->slug,
            'phone' => $this->shippingCarrier->phone,
            'label' => $this->shippingCarrier->label,
            'haveFastShipping' => $this->shippingCarrier->haveFastShipping,
            'description' => $this->shippingCarrier->description ?? '',
            "price" => $this->currencyPriceResource($this->price)
        ];

    }
}