<?php


namespace App\Models;

use Carbon\Carbon;
use App\Traits\HasTranslations;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Shipping
 *
 * @property int $shippingId
 * @property array $name
 * @property string $phone
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * 
 * @property Collection|Address[] $addresses
 * @package App\Models
 */
class ShippingCarriers extends BaseModel
{

    use HasFactory, HasTranslations, Lookable, HasFilters, Searchable;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'shipping_carriers';
    protected $primaryKey = 'shippingCarrierId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    public $allowPluckPrimaryKey = true;
    public $translatable = ['name', 'label', 'description'];

    protected $casts = [
        'name' => 'json',
        'slug' => 'string',
        'label' => 'json',
        'phone' => 'json',
        'default' => 'bool',
        'description' => 'json',
        'haveFastShipping' => 'bool'

    ];


    protected $fillable = [
        'name',
        'slug',
        'addressId',
        'phone',
        'label',
        'description',
        'default',
        'haveFastShipping'


    ];

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'addressId');
    }

    public function allowedFilters(): array
    {
        return [];

    }

    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'phone',
            'createdAt',
            'updatedAt',

        ];
    }

    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'name',
            'value_column' => 'shippingCarrierId',

        ];
    }



    public function priceCities(): HasMany
    {
        return $this->hasMany(ShippingCarrierPrice::class, 'shippingCarrierId');
    }

}